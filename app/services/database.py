import os
from supabase import create_client, Client
from typing import Optional, Dict, List, Any
import logging

logger = logging.getLogger(__name__)

class SupabaseService:
    """Supabase数据库服务类"""

    def __init__(self):
        self.url = os.getenv('SUPABASE_URL')
        self.key = os.getenv('SUPABASE_KEY')
        self.service_key = os.getenv('SUPABASE_SERVICE_KEY')
        self.use_mock = False

        # 检查是否为演示配置
        if (not self.url or not self.key or
            self.url == 'https://your-project.supabase.co' or
            self.key == 'your-anon-key'):
            logger.warning("Using mock database service for demonstration")
            self.use_mock = True
            from app.services.mock_database import mock_db_service
            self.mock_service = mock_db_service
        else:
            try:
                self.client: Client = create_client(self.url, self.key)
                self.service_client: Client = create_client(self.url, self.service_key) if self.service_key else self.client
            except Exception as e:
                logger.error(f"Failed to initialize Supabase client: {e}")
                logger.warning("Falling back to mock database service")
                self.use_mock = True
                from app.services.mock_database import mock_db_service
                self.mock_service = mock_db_service
    
    def get_client(self, use_service_key: bool = False):
        """获取Supabase客户端"""
        if self.use_mock:
            return self.mock_service
        return self.service_client if use_service_key and self.service_key else self.client

    def execute_query(self, table: str, operation: str, data: Optional[Dict] = None,
                     filters: Optional[Dict] = None, use_service_key: bool = False) -> Any:
        """执行数据库查询操作"""
        if self.use_mock:
            return self.mock_service.execute_query(table, operation, data, filters, use_service_key)

        try:
            client = self.get_client(use_service_key)
            query = client.table(table)
            
            if operation == 'select':
                if filters:
                    for key, value in filters.items():
                        if isinstance(value, dict):
                            # 支持复杂查询条件
                            operator = value.get('operator', 'eq')
                            query_value = value.get('value')
                            if operator == 'eq':
                                query = query.eq(key, query_value)
                            elif operator == 'neq':
                                query = query.neq(key, query_value)
                            elif operator == 'gt':
                                query = query.gt(key, query_value)
                            elif operator == 'gte':
                                query = query.gte(key, query_value)
                            elif operator == 'lt':
                                query = query.lt(key, query_value)
                            elif operator == 'lte':
                                query = query.lte(key, query_value)
                            elif operator == 'like':
                                query = query.like(key, query_value)
                            elif operator == 'ilike':
                                query = query.ilike(key, query_value)
                        else:
                            query = query.eq(key, value)
                return query.execute()
            
            elif operation == 'insert':
                return query.insert(data).execute()
            
            elif operation == 'update':
                if filters:
                    for key, value in filters.items():
                        query = query.eq(key, value)
                return query.update(data).execute()
            
            elif operation == 'delete':
                if filters:
                    for key, value in filters.items():
                        query = query.eq(key, value)
                return query.delete().execute()
            
            else:
                raise ValueError(f"Unsupported operation: {operation}")
                
        except Exception as e:
            logger.error(f"Database operation failed: {e}")
            raise
    
    def test_connection(self) -> bool:
        """测试数据库连接"""
        if self.use_mock:
            return self.mock_service.test_connection()

        try:
            result = self.execute_query('admin_users', 'select', filters={'email': '<EMAIL>'})
            return True
        except Exception as e:
            logger.error(f"Database connection test failed: {e}")
            return False

# 全局数据库服务实例
db_service = SupabaseService()
