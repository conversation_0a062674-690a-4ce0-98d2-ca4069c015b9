from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from app.services.database import db_service
from app.models.research_report import ResearchReport
from app.models.user_request import UserRequest
from app.utils.validators import validate_email, validate_url
import logging
import math
from datetime import datetime

logger = logging.getLogger(__name__)

public_bp = Blueprint('public', __name__)

@public_bp.route('/')
def index():
    """主页 - 显示研究报告列表"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = 10
        search_query = request.args.get('search', '').strip()
        
        # 获取报告列表
        reports, total_count = ResearchReport.get_published_reports(
            page=page, 
            per_page=per_page, 
            search_query=search_query
        )
        
        # 计算分页信息
        total_pages = math.ceil(total_count / per_page)
        has_prev = page > 1
        has_next = page < total_pages
        
        pagination = {
            'page': page,
            'per_page': per_page,
            'total': total_count,
            'total_pages': total_pages,
            'has_prev': has_prev,
            'has_next': has_next,
            'prev_num': page - 1 if has_prev else None,
            'next_num': page + 1 if has_next else None
        }
        
        return render_template('public/index.html', 
                             reports=reports, 
                             pagination=pagination,
                             search_query=search_query)
    
    except Exception as e:
        logger.error(f"Error loading index page: {e}")
        flash('加载页面时出现错误，请稍后重试。', 'error')
        return render_template('public/index.html', reports=[], pagination={})

@public_bp.route('/report/<report_id>/analysis')
def view_analysis(report_id):
    """查看分析页面 - 渲染HTML/JS文件"""
    try:
        report = ResearchReport.get_by_id(report_id)
        if not report or not report.get('is_published'):
            flash('报告不存在或未发布。', 'error')
            return redirect(url_for('public.index'))
        
        # 读取分析文件内容
        analysis_content = ResearchReport.get_analysis_content(report['analysis_file_path'])
        
        return render_template('public/analysis.html', 
                             report=report, 
                             analysis_content=analysis_content)
    
    except Exception as e:
        logger.error(f"Error loading analysis page for report {report_id}: {e}")
        flash('加载分析页面时出现错误。', 'error')
        return redirect(url_for('public.index'))

@public_bp.route('/report/<report_id>/report')
def view_report(report_id):
    """查看报告页面 - 渲染Markdown文件"""
    try:
        report = ResearchReport.get_by_id(report_id)
        if not report or not report.get('is_published'):
            flash('报告不存在或未发布。', 'error')
            return redirect(url_for('public.index'))
        
        # 读取并转换Markdown内容
        report_content = ResearchReport.get_report_content(report['report_file_path'])
        
        return render_template('public/report.html', 
                             report=report, 
                             report_content=report_content)
    
    except Exception as e:
        logger.error(f"Error loading report page for report {report_id}: {e}")
        flash('加载报告页面时出现错误。', 'error')
        return redirect(url_for('public.index'))

@public_bp.route('/request-project', methods=['POST'])
def request_project():
    """提交项目请求"""
    try:
        data = request.get_json()
        
        # 验证输入数据
        email = data.get('email', '').strip()
        project_name = data.get('project_name', '').strip()
        official_website = data.get('official_website', '').strip()
        
        errors = []
        
        if not email:
            errors.append('邮箱地址不能为空')
        elif not validate_email(email):
            errors.append('请输入有效的邮箱地址')
        
        if not project_name:
            errors.append('项目名称不能为空')
        elif len(project_name) > 255:
            errors.append('项目名称不能超过255个字符')
        
        if not official_website:
            errors.append('官方网站不能为空')
        elif not validate_url(official_website):
            errors.append('请输入有效的网站URL')
        
        if errors:
            return jsonify({'success': False, 'errors': errors}), 400
        
        # 检查是否已存在相同的请求
        existing_request = UserRequest.get_by_email_and_project(email, project_name)
        if existing_request:
            return jsonify({
                'success': False, 
                'errors': ['您已经提交过相同项目的请求，请耐心等待处理结果。']
            }), 400
        
        # 创建新请求
        request_data = {
            'user_email': email,
            'project_name': project_name,
            'official_website': official_website,
            'status': 'pending'
        }
        
        UserRequest.create(request_data)
        
        # TODO: 发送邮件通知管理员
        
        return jsonify({
            'success': True, 
            'message': '请求已成功提交！我们会尽快处理您的请求，完成后将通过邮件通知您。'
        })
    
    except Exception as e:
        logger.error(f"Error processing project request: {e}")
        return jsonify({
            'success': False, 
            'errors': ['提交请求时出现错误，请稍后重试。']
        }), 500

@public_bp.route('/search')
def search():
    """搜索API端点"""
    try:
        query = request.args.get('q', '').strip()
        page = request.args.get('page', 1, type=int)
        per_page = 10

        if not query:
            return jsonify({'reports': [], 'total': 0})

        reports, total_count = ResearchReport.search_reports(query, page, per_page)

        return jsonify({
            'reports': reports,
            'total': total_count,
            'page': page,
            'per_page': per_page
        })

    except Exception as e:
        logger.error(f"Error in search: {e}")
        return jsonify({'error': '搜索时出现错误'}), 500

@public_bp.route('/health')
def health_check():
    """健康检查端点"""
    try:
        # 检查数据库连接
        from app.services.database import db_service
        db_status = db_service.test_connection()

        return jsonify({
            'status': 'healthy' if db_status else 'unhealthy',
            'database': 'connected' if db_status else 'disconnected',
            'timestamp': datetime.utcnow().isoformat()
        }), 200 if db_status else 503

    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return jsonify({
            'status': 'unhealthy',
            'error': str(e),
            'timestamp': datetime.utcnow().isoformat()
        }), 503
