import os
import uuid
from werkzeug.utils import secure_filename
from flask import current_app
from app.utils.validators import sanitize_filename, validate_file_extension, validate_file_size
import logging

logger = logging.getLogger(__name__)

def allowed_file(filename: str, allowed_extensions: list) -> bool:
    """检查文件是否允许上传"""
    return validate_file_extension(filename, allowed_extensions)

def generate_unique_filename(original_filename: str) -> str:
    """生成唯一的文件名"""
    # 获取文件扩展名
    if '.' in original_filename:
        name, extension = original_filename.rsplit('.', 1)
        extension = extension.lower()
    else:
        name = original_filename
        extension = ''
    
    # 清理原始文件名
    clean_name = sanitize_filename(name)
    
    # 生成唯一标识符
    unique_id = str(uuid.uuid4())[:8]
    
    # 组合新文件名
    if extension:
        new_filename = f"{clean_name}_{unique_id}.{extension}"
    else:
        new_filename = f"{clean_name}_{unique_id}"
    
    return secure_filename(new_filename)

def save_uploaded_file(file, subfolder: str) -> str:
    """保存上传的文件并返回相对路径"""
    try:
        if not file or not file.filename:
            raise ValueError("No file provided")
        
        # 验证文件大小
        file.seek(0, 2)  # 移动到文件末尾
        file_size = file.tell()
        file.seek(0)  # 重置到文件开头
        
        max_size = current_app.config.get('MAX_CONTENT_LENGTH', 16 * 1024 * 1024)
        if not validate_file_size(file_size, max_size):
            raise ValueError(f"File size exceeds maximum allowed size of {max_size} bytes")
        
        # 生成唯一文件名
        filename = generate_unique_filename(file.filename)
        
        # 创建保存目录
        upload_folder = current_app.config['UPLOAD_FOLDER']
        save_dir = os.path.join(upload_folder, subfolder)
        os.makedirs(save_dir, exist_ok=True)
        
        # 保存文件
        file_path = os.path.join(save_dir, filename)
        file.save(file_path)
        
        # 返回相对路径
        relative_path = os.path.join(subfolder, filename)
        
        logger.info(f"File saved successfully: {relative_path}")
        return relative_path
    
    except Exception as e:
        logger.error(f"Error saving file: {e}")
        raise

def delete_file(file_path: str) -> bool:
    """删除文件"""
    try:
        upload_folder = current_app.config['UPLOAD_FOLDER']
        full_path = os.path.join(upload_folder, file_path)
        
        if os.path.exists(full_path):
            os.remove(full_path)
            logger.info(f"File deleted successfully: {file_path}")
            return True
        else:
            logger.warning(f"File not found for deletion: {file_path}")
            return False
    
    except Exception as e:
        logger.error(f"Error deleting file {file_path}: {e}")
        return False

def get_file_info(file_path: str) -> dict:
    """获取文件信息"""
    try:
        upload_folder = current_app.config['UPLOAD_FOLDER']
        full_path = os.path.join(upload_folder, file_path)
        
        if not os.path.exists(full_path):
            return {'exists': False}
        
        stat = os.stat(full_path)
        
        return {
            'exists': True,
            'size': stat.st_size,
            'modified_time': stat.st_mtime,
            'created_time': stat.st_ctime
        }
    
    except Exception as e:
        logger.error(f"Error getting file info for {file_path}: {e}")
        return {'exists': False, 'error': str(e)}

def ensure_upload_directories():
    """确保上传目录存在"""
    try:
        upload_folder = current_app.config['UPLOAD_FOLDER']
        
        # 创建主要目录
        directories = [
            upload_folder,
            os.path.join(upload_folder, 'reports'),
            os.path.join(upload_folder, 'analysis'),
            os.path.join(upload_folder, 'temp')
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
        
        logger.info("Upload directories ensured")
        return True
    
    except Exception as e:
        logger.error(f"Error ensuring upload directories: {e}")
        return False
