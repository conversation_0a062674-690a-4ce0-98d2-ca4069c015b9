from app.services.database import db_service
from typing import Optional, Dict, Any, List, Tuple
import logging
import os
import markdown
import bleach
from flask import current_app

logger = logging.getLogger(__name__)

class ResearchReport:
    """研究报告模型"""
    
    @staticmethod
    def get_by_id(report_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取报告"""
        try:
            result = db_service.execute_query(
                'research_reports', 
                'select', 
                filters={'id': report_id}
            )
            
            if result.data and len(result.data) > 0:
                return result.data[0]
            return None
        
        except Exception as e:
            logger.error(f"Error getting report by ID {report_id}: {e}")
            return None
    
    @staticmethod
    def get_published_reports(page: int = 1, per_page: int = 10, 
                            search_query: str = '') -> Tuple[List[Dict[str, Any]], int]:
        """获取已发布的报告列表（分页）"""
        try:
            offset = (page - 1) * per_page
            
            # 构建查询条件
            filters = {'is_published': True}
            
            # 如果有搜索查询，添加模糊搜索条件
            if search_query:
                filters['project_name'] = {
                    'operator': 'ilike',
                    'value': f'%{search_query}%'
                }
            
            # 获取总数
            count_result = db_service.execute_query(
                'research_reports', 
                'select', 
                filters=filters
            )
            total_count = len(count_result.data) if count_result.data else 0
            
            # 获取分页数据
            # 注意：Supabase的Python客户端可能需要不同的分页方法
            # 这里使用基本的查询，实际使用时可能需要调整
            result = db_service.execute_query(
                'research_reports', 
                'select', 
                filters=filters
            )
            
            if result.data:
                # 手动实现分页（在生产环境中应该在数据库层面实现）
                sorted_data = sorted(result.data, key=lambda x: x['created_at'], reverse=True)
                paginated_data = sorted_data[offset:offset + per_page]
                return paginated_data, total_count
            
            return [], 0
        
        except Exception as e:
            logger.error(f"Error getting published reports: {e}")
            return [], 0
    
    @staticmethod
    def get_all_reports(page: int = 1, per_page: int = 10) -> Tuple[List[Dict[str, Any]], int]:
        """获取所有报告列表（管理员用）"""
        try:
            offset = (page - 1) * per_page
            
            # 获取总数
            count_result = db_service.execute_query('research_reports', 'select')
            total_count = len(count_result.data) if count_result.data else 0
            
            # 获取分页数据
            result = db_service.execute_query('research_reports', 'select')
            
            if result.data:
                # 手动实现分页和排序
                sorted_data = sorted(result.data, key=lambda x: x['created_at'], reverse=True)
                paginated_data = sorted_data[offset:offset + per_page]
                return paginated_data, total_count
            
            return [], 0
        
        except Exception as e:
            logger.error(f"Error getting all reports: {e}")
            return [], 0
    
    @staticmethod
    def search_reports(query: str, page: int = 1, per_page: int = 10) -> Tuple[List[Dict[str, Any]], int]:
        """搜索报告"""
        try:
            filters = {
                'is_published': True,
                'project_name': {
                    'operator': 'ilike',
                    'value': f'%{query}%'
                }
            }
            
            result = db_service.execute_query(
                'research_reports', 
                'select', 
                filters=filters
            )
            
            if result.data:
                total_count = len(result.data)
                offset = (page - 1) * per_page
                sorted_data = sorted(result.data, key=lambda x: x['created_at'], reverse=True)
                paginated_data = sorted_data[offset:offset + per_page]
                return paginated_data, total_count
            
            return [], 0
        
        except Exception as e:
            logger.error(f"Error searching reports: {e}")
            return [], 0
    
    @staticmethod
    def create(report_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """创建新报告"""
        try:
            result = db_service.execute_query(
                'research_reports', 
                'insert', 
                data=report_data
            )
            
            if result.data and len(result.data) > 0:
                return result.data[0]
            return None
        
        except Exception as e:
            logger.error(f"Error creating report: {e}")
            return None
    
    @staticmethod
    def update(report_id: str, update_data: Dict[str, Any]) -> bool:
        """更新报告"""
        try:
            result = db_service.execute_query(
                'research_reports', 
                'update', 
                data=update_data,
                filters={'id': report_id}
            )
            
            return result.data is not None
        
        except Exception as e:
            logger.error(f"Error updating report {report_id}: {e}")
            return False
    
    @staticmethod
    def delete(report_id: str) -> bool:
        """删除报告"""
        try:
            result = db_service.execute_query(
                'research_reports', 
                'delete', 
                filters={'id': report_id}
            )
            
            return result.data is not None
        
        except Exception as e:
            logger.error(f"Error deleting report {report_id}: {e}")
            return False
    
    @staticmethod
    def get_total_count() -> int:
        """获取报告总数"""
        try:
            result = db_service.execute_query('research_reports', 'select')
            return len(result.data) if result.data else 0
        
        except Exception as e:
            logger.error(f"Error getting total count: {e}")
            return 0
    
    @staticmethod
    def get_recent_reports(limit: int = 5) -> List[Dict[str, Any]]:
        """获取最近的报告"""
        try:
            result = db_service.execute_query('research_reports', 'select')
            
            if result.data:
                sorted_data = sorted(result.data, key=lambda x: x['created_at'], reverse=True)
                return sorted_data[:limit]
            
            return []
        
        except Exception as e:
            logger.error(f"Error getting recent reports: {e}")
            return []
    
    @staticmethod
    def get_report_content(file_path: str) -> str:
        """读取并转换Markdown报告内容"""
        try:
            full_path = os.path.join(current_app.config['UPLOAD_FOLDER'], file_path)
            
            if not os.path.exists(full_path):
                return "报告文件不存在。"
            
            with open(full_path, 'r', encoding='utf-8') as f:
                markdown_content = f.read()
            
            # 转换Markdown为HTML
            html_content = markdown.markdown(
                markdown_content, 
                extensions=['tables', 'fenced_code', 'toc']
            )
            
            # 清理HTML以防止XSS攻击
            allowed_tags = [
                'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
                'p', 'br', 'strong', 'em', 'u', 'strike',
                'ul', 'ol', 'li', 'blockquote', 'pre', 'code',
                'table', 'thead', 'tbody', 'tr', 'th', 'td',
                'a', 'img', 'div', 'span'
            ]
            
            allowed_attributes = {
                'a': ['href', 'title'],
                'img': ['src', 'alt', 'title', 'width', 'height'],
                'div': ['class'],
                'span': ['class'],
                'table': ['class'],
                'th': ['class'],
                'td': ['class']
            }
            
            clean_html = bleach.clean(
                html_content, 
                tags=allowed_tags, 
                attributes=allowed_attributes
            )
            
            return clean_html
        
        except Exception as e:
            logger.error(f"Error reading report content from {file_path}: {e}")
            return "读取报告内容时出现错误。"
    
    @staticmethod
    def get_analysis_content(file_path: str) -> str:
        """读取分析页面HTML内容"""
        try:
            full_path = os.path.join(current_app.config['UPLOAD_FOLDER'], file_path)
            
            if not os.path.exists(full_path):
                return "<p>分析文件不存在。</p>"
            
            with open(full_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            # 基本的HTML清理（保留更多标签用于交互式内容）
            allowed_tags = [
                'html', 'head', 'body', 'title', 'meta', 'link', 'style', 'script',
                'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
                'p', 'br', 'strong', 'em', 'u', 'strike',
                'ul', 'ol', 'li', 'blockquote', 'pre', 'code',
                'table', 'thead', 'tbody', 'tr', 'th', 'td',
                'a', 'img', 'div', 'span', 'canvas', 'svg',
                'button', 'input', 'select', 'option', 'textarea'
            ]
            
            # 对于分析页面，我们需要更宽松的属性允许列表
            return html_content  # 暂时返回原始内容，实际部署时需要更严格的清理
        
        except Exception as e:
            logger.error(f"Error reading analysis content from {file_path}: {e}")
            return "<p>读取分析内容时出现错误。</p>"
