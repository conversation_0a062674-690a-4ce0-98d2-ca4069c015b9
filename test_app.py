#!/usr/bin/env python3
"""
应用测试脚本
"""

import os
import sys
import requests
import time
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_database_connection():
    """测试数据库连接"""
    try:
        from app.services.database import db_service
        
        print("测试数据库连接...")
        if db_service.test_connection():
            print("✓ 数据库连接成功")
            return True
        else:
            print("✗ 数据库连接失败")
            return False
    except Exception as e:
        print(f"✗ 数据库连接测试出错: {e}")
        return False

def test_app_startup():
    """测试应用启动"""
    try:
        print("测试应用启动...")
        from app import create_app
        
        app = create_app()
        
        with app.test_client() as client:
            # 测试首页
            response = client.get('/')
            if response.status_code == 200:
                print("✓ 首页访问成功")
            else:
                print(f"✗ 首页访问失败: {response.status_code}")
                return False
            
            # 测试健康检查
            response = client.get('/health')
            if response.status_code in [200, 503]:  # 503也是正常的（数据库未连接时）
                print("✓ 健康检查端点正常")
            else:
                print(f"✗ 健康检查端点异常: {response.status_code}")
                return False
            
            # 测试管理员登录页面
            response = client.get('/admin/login')
            if response.status_code == 200:
                print("✓ 管理员登录页面正常")
            else:
                print(f"✗ 管理员登录页面异常: {response.status_code}")
                return False
        
        print("✓ 应用启动测试通过")
        return True
    
    except Exception as e:
        print(f"✗ 应用启动测试失败: {e}")
        return False

def test_file_structure():
    """测试文件结构"""
    print("检查文件结构...")
    
    required_files = [
        'run.py',
        'requirements.txt',
        'app/__init__.py',
        'app/models/admin_user.py',
        'app/models/research_report.py',
        'app/models/user_request.py',
        'app/views/public.py',
        'app/views/admin.py',
        'app/services/database.py',
        'app/services/email_service.py',
        'app/utils/validators.py',
        'app/utils/file_handler.py',
        'app/utils/security.py',
        'templates/base.html',
        'templates/public/index.html',
        'templates/admin/login.html',
        'static/css/style.css',
        'static/js/main.js',
        'database/schema.sql'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("✗ 缺少以下文件:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        return False
    else:
        print("✓ 所有必需文件都存在")
        return True

def test_environment_config():
    """测试环境配置"""
    print("检查环境配置...")
    
    required_env_vars = [
        'SUPABASE_URL',
        'SUPABASE_KEY',
        'FLASK_SECRET_KEY'
    ]
    
    missing_vars = []
    for var in required_env_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print("✗ 缺少以下环境变量:")
        for var in missing_vars:
            print(f"  - {var}")
        print("请检查 .env 文件")
        return False
    else:
        print("✓ 环境配置正常")
        return True

def test_directories():
    """测试目录结构"""
    print("检查目录结构...")
    
    required_dirs = [
        'uploads',
        'uploads/reports',
        'uploads/analysis',
        'static',
        'static/css',
        'static/js',
        'templates',
        'templates/public',
        'templates/admin'
    ]
    
    for dir_path in required_dirs:
        if not os.path.exists(dir_path):
            os.makedirs(dir_path, exist_ok=True)
            print(f"✓ 创建目录: {dir_path}")
        else:
            print(f"✓ 目录存在: {dir_path}")
    
    return True

def test_sample_data():
    """测试示例数据"""
    try:
        print("检查示例数据...")
        from app.services.database import db_service
        
        # 检查管理员用户
        admin_result = db_service.execute_query(
            'admin_users',
            'select',
            filters={'email': '<EMAIL>'}
        )
        
        if admin_result.data:
            print("✓ 管理员用户存在")
        else:
            print("✗ 管理员用户不存在，请运行 python create_admin.py")
            return False
        
        # 检查示例报告
        reports_result = db_service.execute_query('research_reports', 'select')
        
        if reports_result.data:
            print(f"✓ 存在 {len(reports_result.data)} 个研究报告")
        else:
            print("! 没有研究报告数据")
        
        return True
    
    except Exception as e:
        print(f"✗ 示例数据检查失败: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("=" * 50)
    print("项目研究报告平台 - 系统测试")
    print("=" * 50)
    
    tests = [
        ("文件结构", test_file_structure),
        ("目录结构", test_directories),
        ("环境配置", test_environment_config),
        ("数据库连接", test_database_connection),
        ("应用启动", test_app_startup),
        ("示例数据", test_sample_data)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
            else:
                print(f"测试失败: {test_name}")
        except Exception as e:
            print(f"测试出错: {test_name} - {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有测试通过！应用可以正常运行。")
        print("\n启动应用:")
        print("  python run.py")
        print("\n访问地址:")
        print("  首页: http://localhost:5000")
        print("  管理后台: http://localhost:5000/admin/login")
        print("  默认管理员: <EMAIL> / admin123")
    else:
        print("✗ 部分测试失败，请检查上述错误信息。")
    
    return passed == total

if __name__ == '__main__':
    success = run_all_tests()
    sys.exit(0 if success else 1)
