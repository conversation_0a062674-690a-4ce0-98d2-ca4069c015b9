# 项目研究报告平台 - 设计系统文档

## 概述

本设计系统基于 Material Design 3.0 设计语言，采用现代化的 UI/UX 设计原则，为项目研究报告平台提供一致、可访问且美观的用户界面。

## 设计原则

### 1. 一致性 (Consistency)
- 统一的视觉语言和交互模式
- 可复用的组件和设计模式
- 一致的间距、字体和颜色使用

### 2. 可访问性 (Accessibility)
- WCAG 2.1 AA 级别合规
- 键盘导航支持
- 屏幕阅读器友好
- 高对比度颜色方案

### 3. 响应式设计 (Responsive)
- 移动优先的设计方法
- 流畅的跨设备体验
- 灵活的布局系统

### 4. 性能优化 (Performance)
- 轻量级的组件设计
- 优化的加载体验
- 渐进式增强

## 颜色系统

### 主色调 (Primary Colors)
```css
/* 科技感蓝紫色渐变 */
--primary-50: #f0f4ff;
--primary-100: #e0e9ff;
--primary-200: #c7d6fe;
--primary-300: #a5b8fc;
--primary-400: #8b93f8;
--primary-500: #667eea; /* 主色 */
--primary-600: #5a67d8;
--primary-700: #4c51bf;
--primary-800: #434190;
--primary-900: #3c366b;
```

### 辅助色调 (Secondary Colors)
```css
/* 紫色系 */
--secondary-500: #764ba2; /* 辅助主色 */
```

### 功能色彩 (Semantic Colors)
```css
/* 成功 */
--success-500: #28a745;

/* 警告 */
--warning-500: #ffc107;

/* 错误 */
--danger-500: #dc3545;

/* 信息 */
--info-500: #17a2b8;
```

### 中性色系 (Neutral Colors)
```css
--gray-50: #f8f9fa;
--gray-100: #e9ecef;
--gray-200: #dee2e6;
--gray-300: #ced4da;
--gray-400: #adb5bd;
--gray-500: #6c757d;
--gray-600: #495057;
--gray-700: #343a40;
--gray-800: #212529;
--gray-900: #1a1e21;
```

## 字体系统

### 字体族 (Font Family)
```css
font-family: 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
```

### 字体大小 (Font Sizes)
- **xs**: 0.75rem (12px) - 小标签、辅助文本
- **sm**: 0.875rem (14px) - 正文、表单标签
- **base**: 1rem (16px) - 默认正文
- **lg**: 1.125rem (18px) - 副标题
- **xl**: 1.25rem (20px) - 小标题
- **2xl**: 1.5rem (24px) - 标题
- **3xl**: 1.875rem (30px) - 大标题
- **4xl**: 2.25rem (36px) - 主标题
- **5xl**: 3rem (48px) - 超大标题

### 字体权重 (Font Weights)
- **normal**: 400 - 正文
- **medium**: 500 - 强调文本
- **semibold**: 600 - 副标题
- **bold**: 700 - 标题

## 间距系统

基于 4px 网格系统：

```css
--spacing-1: 0.25rem; /* 4px */
--spacing-2: 0.5rem;  /* 8px */
--spacing-3: 0.75rem; /* 12px */
--spacing-4: 1rem;    /* 16px */
--spacing-5: 1.25rem; /* 20px */
--spacing-6: 1.5rem;  /* 24px */
--spacing-8: 2rem;    /* 32px */
--spacing-10: 2.5rem; /* 40px */
--spacing-12: 3rem;   /* 48px */
--spacing-16: 4rem;   /* 64px */
```

## 组件库

### 按钮 (Buttons)

#### 主要按钮 (Primary Button)
```html
<button class="btn btn-primary">
    主要操作
</button>
```

#### 次要按钮 (Secondary Button)
```html
<button class="btn btn-secondary">
    次要操作
</button>
```

#### 按钮尺寸
- **sm**: 小尺寸按钮
- **base**: 默认尺寸
- **lg**: 大尺寸按钮

### 卡片 (Cards)

#### 基础卡片
```html
<div class="card">
    <div class="card-header">
        <h3>卡片标题</h3>
    </div>
    <div class="card-body">
        <p>卡片内容</p>
    </div>
    <div class="card-footer">
        <button class="btn btn-primary">操作</button>
    </div>
</div>
```

#### 悬停效果卡片
```html
<div class="card card-hover">
    <!-- 卡片内容 -->
</div>
```

### 表单 (Forms)

#### 输入框
```html
<div>
    <label class="form-label">标签</label>
    <input type="text" class="form-input" placeholder="请输入...">
</div>
```

#### 错误状态
```html
<div>
    <label class="form-label">标签</label>
    <input type="text" class="form-input border-red-500" placeholder="请输入...">
    <p class="form-error">错误信息</p>
</div>
```

### 徽章 (Badges)

```html
<span class="badge badge-primary">主要</span>
<span class="badge badge-success">成功</span>
<span class="badge badge-warning">警告</span>
<span class="badge badge-danger">错误</span>
```

### 通知 (Notifications)

```html
<div class="notification notification-success">
    <div class="flex items-center">
        <div class="flex-shrink-0">
            <!-- 图标 -->
        </div>
        <div class="ml-3">
            <p class="text-sm font-medium">成功消息</p>
        </div>
    </div>
</div>
```

## 动画系统

### 过渡动画 (Transitions)
```css
/* 标准过渡 */
transition: all 0.2s ease-in-out;

/* 快速过渡 */
transition: all 0.15s ease-in-out;

/* 慢速过渡 */
transition: all 0.3s ease-in-out;
```

### 关键帧动画 (Keyframe Animations)
- **fade-in**: 淡入效果
- **slide-up**: 向上滑动
- **slide-down**: 向下滑动
- **shimmer**: 骨架屏闪烁效果
- **bounce-soft**: 轻微弹跳效果

## 响应式断点

```css
/* 移动设备 */
@media (max-width: 640px) { /* sm */ }

/* 平板设备 */
@media (min-width: 641px) and (max-width: 1024px) { /* md */ }

/* 桌面设备 */
@media (min-width: 1025px) { /* lg */ }

/* 大屏设备 */
@media (min-width: 1280px) { /* xl */ }
```

## 无障碍性指南

### 颜色对比度
- 正文文字与背景对比度 ≥ 4.5:1
- 大文字与背景对比度 ≥ 3:1
- 非文字元素对比度 ≥ 3:1

### 键盘导航
- 所有交互元素支持 Tab 键导航
- 明显的焦点指示器
- 逻辑的 Tab 顺序

### 屏幕阅读器
- 语义化 HTML 标签
- 适当的 ARIA 标签
- 有意义的链接文本

### 触控目标
- 最小触控目标尺寸：44x44px
- 足够的间距避免误触

## 暗色模式

系统支持自动暗色模式切换：

```css
/* 暗色模式样式 */
.dark .card {
    @apply bg-gray-800 border-gray-700;
}

.dark .text-gray-900 {
    @apply text-white;
}
```

## 使用指南

### 开发者指南
1. 使用预定义的 CSS 类而不是内联样式
2. 遵循组件的标准结构
3. 确保响应式设计的实现
4. 测试无障碍性功能

### 设计师指南
1. 使用设计系统中定义的颜色和字体
2. 保持一致的间距和布局
3. 考虑不同设备的用户体验
4. 设计时考虑无障碍性需求

## 更新日志

### v1.0.0 (2024-01-01)
- 初始设计系统发布
- 基础组件库
- 颜色和字体系统
- 响应式断点定义
