{% extends "admin/base.html" %}

{% block title %}仪表板 - 管理后台{% endblock %}
{% block page_title %}仪表板{% endblock %}

{% block content %}
<div class="row">
    <!-- Statistics Cards -->
    <div class="col-md-3 col-sm-6">
        <div class="stat-card">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stat-number">{{ stats.total_reports or 0 }}</div>
                    <div class="stat-label">研究报告总数</div>
                </div>
                <div class="text-primary">
                    <i class="fas fa-file-alt fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 col-sm-6">
        <div class="stat-card warning">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stat-number">{{ stats.pending_requests or 0 }}</div>
                    <div class="stat-label">待处理请求</div>
                </div>
                <div class="text-warning">
                    <i class="fas fa-clock fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 col-sm-6">
        <div class="stat-card success">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stat-number">{{ (stats.recent_reports | length) or 0 }}</div>
                    <div class="stat-label">本月新增报告</div>
                </div>
                <div class="text-success">
                    <i class="fas fa-plus-circle fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 col-sm-6">
        <div class="stat-card danger">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <div class="stat-number">{{ (stats.recent_requests | length) or 0 }}</div>
                    <div class="stat-label">本月新增请求</div>
                </div>
                <div class="text-danger">
                    <i class="fas fa-inbox fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Recent Reports -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-file-alt me-2"></i>最近的报告
                </h5>
                <a href="{{ url_for('admin.reports') }}" class="btn btn-sm btn-outline-primary">
                    查看全部
                </a>
            </div>
            <div class="card-body">
                {% if stats.recent_reports %}
                    <div class="list-group list-group-flush">
                        {% for report in stats.recent_reports %}
                        <div class="list-group-item d-flex justify-content-between align-items-start">
                            <div class="ms-2 me-auto">
                                <div class="fw-bold">{{ report.project_name }}</div>
                                <small class="text-muted">
                                    创建者: {{ report.creator_name }} | 
                                    {{ moment(report.created_at).fromNow() if report.created_at else '未知时间' }}
                                </small>
                            </div>
                            <span class="badge bg-{{ 'success' if report.is_published else 'secondary' }} rounded-pill">
                                {{ '已发布' if report.is_published else '草稿' }}
                            </span>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-3 text-muted">
                        <i class="fas fa-file-alt fa-2x mb-2"></i>
                        <p class="mb-0">暂无报告</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Recent Requests -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-inbox me-2"></i>最近的请求
                </h5>
                <a href="{{ url_for('admin.requests') }}" class="btn btn-sm btn-outline-primary">
                    查看全部
                </a>
            </div>
            <div class="card-body">
                {% if stats.recent_requests %}
                    <div class="list-group list-group-flush">
                        {% for request in stats.recent_requests %}
                        <div class="list-group-item d-flex justify-content-between align-items-start">
                            <div class="ms-2 me-auto">
                                <div class="fw-bold">{{ request.project_name }}</div>
                                <small class="text-muted">
                                    {{ request.user_email }} | 
                                    {{ moment(request.created_at).fromNow() if request.created_at else '未知时间' }}
                                </small>
                            </div>
                            <span class="badge bg-{{ 'warning' if request.status == 'pending' else 'info' if request.status == 'processing' else 'success' if request.status == 'completed' else 'danger' }} rounded-pill">
                                {% if request.status == 'pending' %}待处理
                                {% elif request.status == 'processing' %}处理中
                                {% elif request.status == 'completed' %}已完成
                                {% elif request.status == 'rejected' %}已拒绝
                                {% else %}{{ request.status }}
                                {% endif %}
                            </span>
                        </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center py-3 text-muted">
                        <i class="fas fa-inbox fa-2x mb-2"></i>
                        <p class="mb-0">暂无请求</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>快速操作
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 col-sm-6 mb-3">
                        <a href="{{ url_for('admin.create_report') }}" class="btn btn-primary w-100">
                            <i class="fas fa-plus me-2"></i>添加新报告
                        </a>
                    </div>
                    <div class="col-md-3 col-sm-6 mb-3">
                        <a href="{{ url_for('admin.reports') }}" class="btn btn-outline-primary w-100">
                            <i class="fas fa-list me-2"></i>管理报告
                        </a>
                    </div>
                    <div class="col-md-3 col-sm-6 mb-3">
                        <a href="{{ url_for('admin.requests') }}" class="btn btn-outline-warning w-100">
                            <i class="fas fa-inbox me-2"></i>处理请求
                        </a>
                    </div>
                    <div class="col-md-3 col-sm-6 mb-3">
                        <a href="{{ url_for('public.index') }}" target="_blank" class="btn btn-outline-info w-100">
                            <i class="fas fa-external-link-alt me-2"></i>查看网站
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Status -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>系统状态
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-database text-success me-2"></i>
                            <span>数据库连接: <strong class="text-success">正常</strong></span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-folder text-success me-2"></i>
                            <span>文件存储: <strong class="text-success">正常</strong></span>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-envelope text-warning me-2"></i>
                            <span>邮件服务: <strong class="text-warning">待配置</strong></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // 自动刷新统计数据（每5分钟）
    setInterval(function() {
        location.reload();
    }, 5 * 60 * 1000);
    
    // 添加动画效果
    $('.stat-card').each(function(index) {
        $(this).delay(index * 100).fadeIn();
    });
});
</script>
{% endblock %}
