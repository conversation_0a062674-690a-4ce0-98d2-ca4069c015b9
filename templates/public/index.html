{% extends "base.html" %}

{% block title %}项目研究报告列表 - 项目研究报告平台{% endblock %}
{% block description %}浏览最新的项目研究报告，获取深度技术分析和项目评估{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="bg-gradient-to-r from-blue-600 to-purple-700 rounded-2xl p-8 mb-8 text-white">
    <div class="max-w-4xl mx-auto text-center">
        <h1 class="text-4xl md:text-5xl font-bold mb-4">
            项目研究报告平台
        </h1>
        <p class="text-xl md:text-2xl mb-6 text-blue-100">
            深度技术分析 · 专业项目评估 · 数据驱动决策
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <button type="button" class="btn btn-lg bg-white text-blue-600 hover:bg-gray-100" onclick="document.getElementById('search-input').focus()">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                </svg>
                浏览报告
            </button>
            <button type="button" class="btn btn-lg border-2 border-white text-white hover:bg-white hover:text-blue-600" data-modal-target="requestModal">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                申请新项目
            </button>
        </div>
    </div>
</div>

<!-- Stats Section -->
<div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
    <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div class="flex items-center">
            <div class="p-3 bg-blue-100 dark:bg-blue-900 rounded-lg">
                <svg class="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
            </div>
            <div class="ml-4">
                <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ reports|length if reports else 0 }}</p>
                <p class="text-gray-600 dark:text-gray-400">研究报告</p>
            </div>
        </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div class="flex items-center">
            <div class="p-3 bg-green-100 dark:bg-green-900 rounded-lg">
                <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
            </div>
            <div class="ml-4">
                <p class="text-2xl font-bold text-gray-900 dark:text-white">100+</p>
                <p class="text-gray-600 dark:text-gray-400">项目分析</p>
            </div>
        </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div class="flex items-center">
            <div class="p-3 bg-purple-100 dark:bg-purple-900 rounded-lg">
                <svg class="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                </svg>
            </div>
            <div class="ml-4">
                <p class="text-2xl font-bold text-gray-900 dark:text-white">1000+</p>
                <p class="text-gray-600 dark:text-gray-400">用户访问</p>
            </div>
        </div>
    </div>
</div>

<!-- Search and Filter Section -->
<div class="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 mb-8">
    <form method="GET" action="{{ url_for('public.index') }}" class="space-y-4">
        <div class="flex flex-col md:flex-row gap-4">
            <div class="flex-1">
                <label for="search-input" class="sr-only">搜索项目</label>
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    <input type="text" id="search-input" name="search"
                           class="form-input pl-10 w-full"
                           placeholder="搜索项目名称、技术栈或关键词..."
                           value="{{ search_query or '' }}">
                </div>
            </div>
            <div class="flex gap-2">
                <button type="submit" class="btn btn-primary">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    搜索
                </button>
                {% if search_query %}
                <a href="{{ url_for('public.index') }}" class="btn btn-secondary">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    清除
                </a>
                {% endif %}
            </div>
        </div>

        {% if search_query %}
        <div class="flex items-center text-sm text-gray-600 dark:text-gray-400">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            搜索 "{{ search_query }}" 的结果
        </div>
        {% endif %}
    </form>
</div>

        <!-- Search Bar -->
        <div class="row mb-4">
            <div class="col-md-8 mx-auto">
                <form method="GET" action="{{ url_for('public.index') }}">
                    <div class="input-group">
                        <input type="text" class="form-control" name="search" 
                               placeholder="搜索项目名称..." 
                               value="{{ search_query or '' }}">
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-search"></i> 搜索
                        </button>
                        {% if search_query %}
                        <a href="{{ url_for('public.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i> 清除
                        </a>
                        {% endif %}
                    </div>
                </form>
            </div>
        </div>

<!-- Reports Grid -->
{% if reports %}
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
    {% for report in reports %}
    <div class="card card-hover group">
        <!-- Card Header with Project Image -->
        <div class="relative h-48 bg-gradient-to-br from-blue-500 to-purple-600 overflow-hidden">
            <div class="absolute inset-0 bg-black bg-opacity-20"></div>
            <div class="absolute inset-0 flex items-center justify-center">
                <div class="text-center text-white">
                    <svg class="w-16 h-16 mx-auto mb-2 opacity-80" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    <h3 class="text-lg font-bold">{{ report.project_name }}</h3>
                </div>
            </div>
            <!-- Status Badge -->
            <div class="absolute top-4 right-4">
                <span class="badge badge-success">已发布</span>
            </div>
        </div>

        <!-- Card Body -->
        <div class="card-body">
            <div class="mb-4">
                {% if report.description %}
                <p class="text-gray-600 dark:text-gray-400 text-sm line-clamp-3">
                    {{ report.description[:150] }}{% if report.description|length > 150 %}...{% endif %}
                </p>
                {% else %}
                <p class="text-gray-500 dark:text-gray-500 text-sm italic">暂无项目描述</p>
                {% endif %}
            </div>

            <!-- Meta Information -->
            <div class="space-y-2 mb-4">
                <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                    <span>{{ report.creator_name }}</span>
                </div>
                <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0h6m-6 0l-1 12a2 2 0 002 2h6a2 2 0 002-2L16 7"></path>
                    </svg>
                    <span>{{ report.created_at[:10] if report.created_at else '未知' }}</span>
                </div>
                {% if report.official_website %}
                <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                    </svg>
                    <a href="{{ report.official_website }}" target="_blank" class="text-blue-600 dark:text-blue-400 hover:underline truncate">
                        {{ report.official_website.replace('https://', '').replace('http://', '') }}
                    </a>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Card Footer -->
        <div class="card-footer">
            <div class="flex flex-col sm:flex-row gap-2">
                <a href="{{ url_for('public.view_analysis', report_id=report.id) }}"
                   class="btn btn-primary flex-1 text-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    分析页面
                </a>
                <a href="{{ url_for('public.view_report', report_id=report.id) }}"
                   class="btn btn-secondary flex-1 text-center">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    研究报告
                </a>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Pagination -->
{% if pagination and pagination.total_pages > 1 %}
<nav aria-label="报告分页" class="mt-8">
    <div class="flex items-center justify-between">
        <div class="flex-1 flex justify-between sm:hidden">
            {% if pagination.has_prev %}
            <a href="{{ url_for('public.index', page=pagination.prev_num, search=search_query) }}"
               class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                上一页
            </a>
            {% endif %}
            {% if pagination.has_next %}
            <a href="{{ url_for('public.index', page=pagination.next_num, search=search_query) }}"
               class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                下一页
            </a>
            {% endif %}
        </div>

        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
                <p class="text-sm text-gray-700 dark:text-gray-300">
                    显示第 <span class="font-medium">{{ (pagination.page - 1) * 10 + 1 }}</span> 到
                    <span class="font-medium">{{ pagination.page * 10 if pagination.page * 10 < pagination.total else pagination.total }}</span> 项，
                    共 <span class="font-medium">{{ pagination.total }}</span> 项结果
                </p>
            </div>
            <div>
                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                    {% if pagination.has_prev %}
                    <a href="{{ url_for('public.index', page=pagination.prev_num, search=search_query) }}"
                       class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                    </a>
                    {% endif %}

                    {% for page_num in range(1, pagination.total_pages + 1) %}
                        {% if page_num == pagination.page %}
                        <span class="relative inline-flex items-center px-4 py-2 border border-blue-500 bg-blue-50 text-sm font-medium text-blue-600">
                            {{ page_num }}
                        </span>
                        {% elif page_num <= 3 or page_num > pagination.total_pages - 3 or (page_num >= pagination.page - 1 and page_num <= pagination.page + 1) %}
                        <a href="{{ url_for('public.index', page=page_num, search=search_query) }}"
                           class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                            {{ page_num }}
                        </a>
                        {% elif page_num == 4 or page_num == pagination.total_pages - 3 %}
                        <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                            ...
                        </span>
                        {% endif %}
                    {% endfor %}

                    {% if pagination.has_next %}
                    <a href="{{ url_for('public.index', page=pagination.next_num, search=search_query) }}"
                       class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                        </svg>
                    </a>
                    {% endif %}
                </nav>
            </div>
        </div>
    </div>
</nav>
{% endif %}

{% else %}
<!-- Empty State -->
<div class="text-center py-16">
    <div class="mx-auto max-w-md">
        {% if search_query %}
        <svg class="mx-auto h-24 w-24 text-gray-400 mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
        </svg>
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">没有找到匹配的项目</h3>
        <p class="text-gray-500 dark:text-gray-400 mb-6">
            尝试使用不同的关键词搜索，或者申请添加新项目
        </p>
        {% else %}
        <svg class="mx-auto h-24 w-24 text-gray-400 mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
        </svg>
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">暂无研究报告</h3>
        <p class="text-gray-500 dark:text-gray-400 mb-6">
            成为第一个申请项目研究的用户
        </p>
        {% endif %}

        <button type="button" class="btn btn-primary" data-modal-target="requestModal">
            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            申请新项目
        </button>
    </div>
</div>
{% endif %}

        <!-- Can't find project link -->
        <div class="text-center mt-4">
            <p class="text-muted">
                找不到您想要的项目？
                <a href="#" data-bs-toggle="modal" data-bs-target="#requestModal" class="text-decoration-none">
                    <i class="fas fa-plus-circle me-1"></i>申请研究新项目
                </a>
            </p>
        </div>
    </div>
</div>

<!-- Request Project Modal -->
<div id="requestModal" class="fixed inset-0 z-50 hidden overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <!-- Background overlay -->
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity modal-backdrop" aria-hidden="true"></div>

        <!-- Modal panel -->
        <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
            <div class="sm:flex sm:items-start">
                <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 dark:bg-blue-900 sm:mx-0 sm:h-10 sm:w-10">
                    <svg class="h-6 w-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                </div>
                <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white" id="modal-title">
                        申请项目研究
                    </h3>
                    <div class="mt-4">
                        <div class="bg-blue-50 dark:bg-blue-900/50 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm text-blue-700 dark:text-blue-300">
                                        请提交您希望研究的项目。由于计算资源限制，报告生成可能会有延迟。完成后我们会通过邮件通知您，谢谢！
                                    </p>
                                </div>
                            </div>
                        </div>

                        <form id="requestForm" class="space-y-4">
                            <div>
                                <label for="userEmail" class="form-label">
                                    邮箱地址 <span class="text-red-500">*</span>
                                </label>
                                <input type="email" id="userEmail" name="email" class="form-input" required
                                       placeholder="<EMAIL>">
                                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">我们将通过此邮箱通知您报告完成情况</p>
                            </div>

                            <div>
                                <label for="projectName" class="form-label">
                                    项目名称 <span class="text-red-500">*</span>
                                </label>
                                <input type="text" id="projectName" name="project_name" class="form-input" required
                                       placeholder="例如：React.js">
                            </div>

                            <div>
                                <label for="officialWebsite" class="form-label">
                                    官方网站 <span class="text-red-500">*</span>
                                </label>
                                <input type="url" id="officialWebsite" name="official_website" class="form-input" required
                                       placeholder="https://example.com">
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                <button type="button" id="submitRequest" class="btn btn-primary w-full sm:w-auto sm:ml-3">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                    </svg>
                    提交申请
                </button>
                <button type="button" data-modal-close class="btn btn-secondary w-full mt-3 sm:mt-0 sm:w-auto">
                    取消
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 模态框功能
function showModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('hidden');
        document.body.classList.add('overflow-hidden');

        // 添加动画
        setTimeout(() => {
            modal.querySelector('.modal-backdrop').classList.add('opacity-75');
            modal.querySelector('[role="dialog"]').classList.add('opacity-100', 'translate-y-0');
        }, 10);
    }
}

function hideModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.querySelector('.modal-backdrop').classList.remove('opacity-75');
        modal.querySelector('[role="dialog"]').classList.remove('opacity-100', 'translate-y-0');

        setTimeout(() => {
            modal.classList.add('hidden');
            document.body.classList.remove('overflow-hidden');
        }, 300);
    }
}

// 项目申请表单处理
document.getElementById('submitRequest').addEventListener('click', function() {
    const form = document.getElementById('requestForm');
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);

    // 显示加载状态
    const submitBtn = this;
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<div class="loading-spinner mr-2"></div>提交中...';
    submitBtn.disabled = true;

    // 基本验证
    if (!data.email || !data.project_name || !data.official_website) {
        showNotification('请填写所有必填字段', 'error');
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
        return;
    }

    // 邮箱格式验证
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(data.email)) {
        showNotification('请输入有效的邮箱地址', 'error');
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
        return;
    }

    // URL格式验证
    const urlRegex = /^https?:\/\/.+/;
    if (!urlRegex.test(data.official_website)) {
        showNotification('请输入有效的网站地址（以http://或https://开头）', 'error');
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
        return;
    }

    // 提交请求
    fetch('{{ url_for("public.request_project") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token() }}'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showNotification(result.message || '申请提交成功！我们会尽快处理您的请求。', 'success');
            hideModal('requestModal');
            form.reset();
        } else {
            showNotification('错误：' + (result.errors ? result.errors.join(', ') : '提交失败'), 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('提交时出现错误，请稍后重试', 'error');
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
});

// 通知功能
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type} animate-slide-down`;

    const iconMap = {
        success: '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>',
        error: '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg>',
        warning: '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path></svg>',
        info: '<svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path></svg>'
    };

    notification.innerHTML = `
        <div class="flex items-center">
            <div class="flex-shrink-0">
                ${iconMap[type] || iconMap.info}
            </div>
            <div class="ml-3 flex-1">
                <p class="text-sm font-medium">${message}</p>
            </div>
            <div class="ml-4 flex-shrink-0">
                <button class="inline-flex text-white hover:text-gray-200 focus:outline-none" onclick="this.parentElement.parentElement.parentElement.remove()">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </button>
            </div>
        </div>
    `;

    // 添加到页面
    let container = document.getElementById('notification-container');
    if (!container) {
        container = document.createElement('div');
        container.id = 'notification-container';
        container.className = 'fixed top-20 right-4 z-50 space-y-2';
        document.body.appendChild(container);
    }

    container.appendChild(notification);

    // 自动移除
    setTimeout(() => {
        notification.remove();
    }, 5000);
}

// 搜索功能增强
const searchInput = document.getElementById('search-input');
if (searchInput) {
    let searchTimeout;

    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        const query = this.value.trim();

        if (query.length >= 2) {
            searchTimeout = setTimeout(() => {
                // 这里可以实现实时搜索建议
                console.log('Searching for:', query);
            }, 300);
        }
    });
}
</script>
{% endblock %}
