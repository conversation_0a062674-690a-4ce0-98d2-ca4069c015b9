{% extends "base.html" %}

{% block title %}项目研究报告列表 - 项目研究报告平台{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2">
                <i class="fas fa-list me-2"></i>项目研究报告
            </h1>
            <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#requestModal">
                <i class="fas fa-plus me-1"></i>申请新项目
            </button>
        </div>

        <!-- Search Bar -->
        <div class="row mb-4">
            <div class="col-md-8 mx-auto">
                <form method="GET" action="{{ url_for('public.index') }}">
                    <div class="input-group">
                        <input type="text" class="form-control" name="search" 
                               placeholder="搜索项目名称..." 
                               value="{{ search_query or '' }}">
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-search"></i> 搜索
                        </button>
                        {% if search_query %}
                        <a href="{{ url_for('public.index') }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times"></i> 清除
                        </a>
                        {% endif %}
                    </div>
                </form>
            </div>
        </div>

        <!-- Reports List -->
        {% if reports %}
        <div class="card">
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>项目名称</th>
                                <th>创建者</th>
                                <th>创建时间</th>
                                <th>最后更新</th>
                                <th class="text-center">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for report in reports %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-file-alt text-primary me-2"></i>
                                        <div>
                                            <strong>{{ report.project_name }}</strong>
                                            {% if report.description %}
                                            <br><small class="text-muted">{{ report.description[:100] }}{% if report.description|length > 100 %}...{% endif %}</small>
                                            {% endif %}
                                        </div>
                                    </div>
                                </td>
                                <td>{{ report.creator_name }}</td>
                                <td>
                                    <small class="text-muted">
                                        {{ report.created_at[:10] if report.created_at else '未知' }}
                                    </small>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        {{ report.updated_at[:10] if report.updated_at else '未知' }}
                                    </small>
                                </td>
                                <td class="text-center">
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('public.view_analysis', report_id=report.id) }}" 
                                           class="btn btn-sm btn-outline-primary" title="查看分析页面">
                                            <i class="fas fa-chart-bar"></i> 分析
                                        </a>
                                        <a href="{{ url_for('public.view_report', report_id=report.id) }}" 
                                           class="btn btn-sm btn-outline-success" title="查看研究报告">
                                            <i class="fas fa-file-text"></i> 报告
                                        </a>
                                        {% if report.official_website %}
                                        <a href="{{ report.official_website }}" target="_blank" 
                                           class="btn btn-sm btn-outline-info" title="访问官方网站">
                                            <i class="fas fa-external-link-alt"></i>
                                        </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Pagination -->
        {% if pagination and pagination.total_pages > 1 %}
        <nav aria-label="报告分页" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if pagination.has_prev %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('public.index', page=pagination.prev_num, search=search_query) }}">
                        <i class="fas fa-chevron-left"></i> 上一页
                    </a>
                </li>
                {% endif %}
                
                {% for page_num in range(1, pagination.total_pages + 1) %}
                    {% if page_num == pagination.page %}
                    <li class="page-item active">
                        <span class="page-link">{{ page_num }}</span>
                    </li>
                    {% elif page_num <= 3 or page_num > pagination.total_pages - 3 or (page_num >= pagination.page - 1 and page_num <= pagination.page + 1) %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('public.index', page=page_num, search=search_query) }}">{{ page_num }}</a>
                    </li>
                    {% elif page_num == 4 or page_num == pagination.total_pages - 3 %}
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>
                    {% endif %}
                {% endfor %}
                
                {% if pagination.has_next %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for('public.index', page=pagination.next_num, search=search_query) }}">
                        下一页 <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}

        {% else %}
        <!-- No Reports Found -->
        <div class="text-center py-5">
            <i class="fas fa-search fa-3x text-muted mb-3"></i>
            <h3 class="text-muted">
                {% if search_query %}
                    没有找到匹配的项目
                {% else %}
                    暂无研究报告
                {% endif %}
            </h3>
            <p class="text-muted">
                {% if search_query %}
                    尝试使用不同的关键词搜索，或者
                {% endif %}
                <a href="#" data-bs-toggle="modal" data-bs-target="#requestModal" class="text-decoration-none">
                    申请添加新项目
                </a>
            </p>
        </div>
        {% endif %}

        <!-- Can't find project link -->
        <div class="text-center mt-4">
            <p class="text-muted">
                找不到您想要的项目？
                <a href="#" data-bs-toggle="modal" data-bs-target="#requestModal" class="text-decoration-none">
                    <i class="fas fa-plus-circle me-1"></i>申请研究新项目
                </a>
            </p>
        </div>
    </div>
</div>

<!-- Request Project Modal -->
<div class="modal fade" id="requestModal" tabindex="-1" aria-labelledby="requestModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="requestModalLabel">
                    <i class="fas fa-plus-circle me-2"></i>申请项目研究
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    请提交您希望研究的项目。由于计算资源限制，报告生成可能会有延迟。完成后我们会通过邮件通知您，谢谢！
                </div>
                
                <form id="requestForm">
                    <div class="mb-3">
                        <label for="userEmail" class="form-label">邮箱地址 <span class="text-danger">*</span></label>
                        <input type="email" class="form-control" id="userEmail" name="email" required>
                        <div class="form-text">我们将通过此邮箱通知您报告完成情况</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="projectName" class="form-label">项目名称 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="projectName" name="project_name" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="officialWebsite" class="form-label">官方网站 <span class="text-danger">*</span></label>
                        <input type="url" class="form-control" id="officialWebsite" name="official_website" 
                               placeholder="https://example.com" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="submitRequest">
                    <i class="fas fa-paper-plane me-1"></i>提交申请
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 项目申请表单处理
document.getElementById('submitRequest').addEventListener('click', function() {
    const form = document.getElementById('requestForm');
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);
    
    // 基本验证
    if (!data.email || !data.project_name || !data.official_website) {
        alert('请填写所有必填字段');
        return;
    }
    
    // 提交请求
    fetch('{{ url_for("public.request_project") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token() }}'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            alert(result.message);
            bootstrap.Modal.getInstance(document.getElementById('requestModal')).hide();
            form.reset();
        } else {
            alert('错误：' + (result.errors ? result.errors.join('\n') : '提交失败'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('提交时出现错误，请稍后重试');
    });
});
</script>
{% endblock %}
