@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义基础样式 */
@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply font-sans text-gray-800 bg-gray-50 antialiased;
    font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;
  }
  
  /* 无障碍性：减少动画 */
  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
  
  /* Focus 样式 */
  *:focus {
    @apply outline-none ring-2 ring-primary-500 ring-offset-2;
  }
  
  /* 滚动条样式 */
  ::-webkit-scrollbar {
    @apply w-2;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;
  }
  
  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
  }
}

/* 自定义组件样式 */
@layer components {
  /* 按钮组件 */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-primary {
    @apply bg-gradient-to-r from-primary-500 to-secondary-500 text-white hover:from-primary-600 hover:to-secondary-600 focus:ring-primary-500 shadow-md hover:shadow-lg hover:-translate-y-0.5;
  }
  
  .btn-secondary {
    @apply bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus:ring-primary-500 shadow-sm hover:shadow-md;
  }
  
  .btn-success {
    @apply bg-success-500 text-white hover:bg-success-600 focus:ring-success-500 shadow-md hover:shadow-lg hover:-translate-y-0.5;
  }
  
  .btn-warning {
    @apply bg-warning-500 text-white hover:bg-warning-600 focus:ring-warning-500 shadow-md hover:shadow-lg hover:-translate-y-0.5;
  }
  
  .btn-danger {
    @apply bg-danger-500 text-white hover:bg-danger-600 focus:ring-danger-500 shadow-md hover:shadow-lg hover:-translate-y-0.5;
  }
  
  .btn-sm {
    @apply px-3 py-1.5 text-xs;
  }
  
  .btn-lg {
    @apply px-6 py-3 text-base;
  }
  
  /* 卡片组件 */
  .card {
    @apply bg-white rounded-xl shadow-soft border border-gray-100 overflow-hidden transition-all duration-300;
  }
  
  .card-hover {
    @apply hover:shadow-medium hover:-translate-y-1;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-gray-100 bg-gray-50/50;
  }
  
  .card-body {
    @apply px-6 py-4;
  }
  
  .card-footer {
    @apply px-6 py-4 border-t border-gray-100 bg-gray-50/50;
  }
  
  /* 表单组件 */
  .form-input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200;
  }
  
  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-2;
  }
  
  .form-error {
    @apply mt-1 text-sm text-danger-600;
  }
  
  /* 导航组件 */
  .nav-link {
    @apply flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 hover:bg-gray-100 focus:bg-gray-100;
  }
  
  .nav-link-active {
    @apply bg-primary-50 text-primary-700 border-r-2 border-primary-500;
  }
  
  /* 徽章组件 */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-primary {
    @apply bg-primary-100 text-primary-800;
  }
  
  .badge-success {
    @apply bg-success-100 text-success-800;
  }
  
  .badge-warning {
    @apply bg-warning-100 text-warning-800;
  }
  
  .badge-danger {
    @apply bg-danger-100 text-danger-800;
  }
  
  .badge-gray {
    @apply bg-gray-100 text-gray-800;
  }
  
  /* 骨架屏动画 */
  .skeleton {
    @apply bg-gray-200 rounded animate-pulse;
  }
  
  .shimmer {
    @apply relative overflow-hidden;
  }
  
  .shimmer::after {
    @apply absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-60;
    content: '';
    animation: shimmer 2s infinite;
  }
  
  /* 渐变背景 */
  .gradient-primary {
    @apply bg-gradient-to-r from-primary-500 to-secondary-500;
  }
  
  .gradient-success {
    @apply bg-gradient-to-r from-success-400 to-success-600;
  }
  
  /* 文本渐变 */
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent;
  }
  
  /* 玻璃态效果 */
  .glass {
    @apply bg-white/80 backdrop-blur-sm border border-white/20;
  }
  
  /* 暗色模式样式 */
  .dark .card {
    @apply bg-gray-800 border-gray-700;
  }
  
  .dark .form-input {
    @apply bg-gray-800 border-gray-600 text-white placeholder-gray-400;
  }
  
  .dark .nav-link {
    @apply text-gray-300 hover:bg-gray-700 hover:text-white;
  }
  
  .dark .nav-link-active {
    @apply bg-primary-900/50 text-primary-300;
  }
}

/* 自定义工具类 */
@layer utilities {
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }
  
  .backdrop-blur-xs {
    backdrop-filter: blur(2px);
  }
  
  /* 安全区域适配 */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }
  
  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }
  
  /* 触控目标最小尺寸 */
  .touch-target {
    @apply min-w-[44px] min-h-[44px];
  }
}
