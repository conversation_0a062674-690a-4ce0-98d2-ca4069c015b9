/* 自定义组件样式 - 基于 Tailwind CSS */

/* 按钮组件 */
.btn {
  @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-primary {
  @apply bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:from-blue-600 hover:to-purple-700 focus:ring-blue-500 shadow-md hover:shadow-lg hover:-translate-y-0.5;
}

.btn-secondary {
  @apply bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus:ring-blue-500 shadow-sm hover:shadow-md;
}

.btn-success {
  @apply bg-green-500 text-white hover:bg-green-600 focus:ring-green-500 shadow-md hover:shadow-lg hover:-translate-y-0.5;
}

.btn-warning {
  @apply bg-yellow-500 text-white hover:bg-yellow-600 focus:ring-yellow-500 shadow-md hover:shadow-lg hover:-translate-y-0.5;
}

.btn-danger {
  @apply bg-red-500 text-white hover:bg-red-600 focus:ring-red-500 shadow-md hover:shadow-lg hover:-translate-y-0.5;
}

.btn-sm {
  @apply px-3 py-1.5 text-xs;
}

.btn-lg {
  @apply px-6 py-3 text-base;
}

/* 卡片组件 */
.card {
  @apply bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden transition-all duration-300;
}

.card-hover {
  @apply hover:shadow-lg hover:-translate-y-1;
}

.card-header {
  @apply px-6 py-4 border-b border-gray-100 bg-gray-50;
}

.card-body {
  @apply px-6 py-4;
}

.card-footer {
  @apply px-6 py-4 border-t border-gray-100 bg-gray-50;
}

/* 表单组件 */
.form-input {
  @apply block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

.form-error {
  @apply mt-1 text-sm text-red-600;
}

/* 导航组件 */
.nav-link {
  @apply flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 hover:bg-gray-100 focus:bg-gray-100;
}

.nav-link-active {
  @apply bg-blue-50 text-blue-700 border-r-2 border-blue-500;
}

/* 徽章组件 */
.badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.badge-primary {
  @apply bg-blue-100 text-blue-800;
}

.badge-success {
  @apply bg-green-100 text-green-800;
}

.badge-warning {
  @apply bg-yellow-100 text-yellow-800;
}

.badge-danger {
  @apply bg-red-100 text-red-800;
}

.badge-gray {
  @apply bg-gray-100 text-gray-800;
}

/* 骨架屏动画 */
.skeleton {
  @apply bg-gray-200 rounded animate-pulse;
}

.shimmer {
  @apply relative overflow-hidden bg-gray-200 rounded;
}

.shimmer::after {
  content: '';
  @apply absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent opacity-60;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* 渐变背景 */
.gradient-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-success {
  background: linear-gradient(135deg, #4ade80 0%, #22c55e 100%);
}

/* 文本渐变 */
.text-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 玻璃态效果 */
.glass {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 暗色模式样式 */
.dark .card {
  @apply bg-gray-800 border-gray-700;
}

.dark .form-input {
  @apply bg-gray-800 border-gray-600 text-white placeholder-gray-400;
}

.dark .nav-link {
  @apply text-gray-300 hover:bg-gray-700 hover:text-white;
}

.dark .nav-link-active {
  @apply bg-blue-900 text-blue-300;
}

/* 自定义工具类 */
.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.text-shadow-lg {
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* 触控目标最小尺寸 */
.touch-target {
  min-width: 44px;
  min-height: 44px;
}

/* 响应式图片 */
.responsive-img {
  @apply w-full h-auto object-cover;
}

/* 加载动画 */
.loading-spinner {
  @apply inline-block w-4 h-4 border-2 border-gray-300 border-t-blue-500 rounded-full animate-spin;
}

/* 通知样式 */
.notification {
  @apply fixed top-4 right-4 z-50 max-w-sm p-4 rounded-lg shadow-lg transform transition-all duration-300;
}

.notification-success {
  @apply bg-green-500 text-white;
}

.notification-error {
  @apply bg-red-500 text-white;
}

.notification-warning {
  @apply bg-yellow-500 text-white;
}

.notification-info {
  @apply bg-blue-500 text-white;
}
