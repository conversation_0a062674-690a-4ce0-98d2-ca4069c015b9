#!/usr/bin/env python3
"""
创建管理员用户的脚本
"""

import os
import sys
from werkzeug.security import generate_password_hash
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app.services.database import db_service

def create_admin_user():
    """创建管理员用户"""
    try:
        # 检查是否已存在管理员
        existing_admin = db_service.execute_query(
            'admin_users',
            'select',
            filters={'email': '<EMAIL>'}
        )
        
        if existing_admin.data:
            print("管理员用户已存在")
            return
        
        # 创建管理员用户
        admin_data = {
            'email': '<EMAIL>',
            'password_hash': generate_password_hash('admin123'),
            'name': '系统管理员',
            'is_active': True
        }
        
        result = db_service.execute_query(
            'admin_users',
            'insert',
            data=admin_data
        )
        
        if result.data:
            print("管理员用户创建成功！")
            print("邮箱: <EMAIL>")
            print("密码: admin123")
        else:
            print("创建管理员用户失败")
    
    except Exception as e:
        print(f"创建管理员用户时出错: {e}")

def create_sample_data():
    """创建示例数据"""
    try:
        # 创建示例报告
        sample_reports = [
            {
                'project_name': 'React.js',
                'official_website': 'https://reactjs.org',
                'creator_name': '研究员A',
                'report_file_path': 'reports/react_report.md',
                'analysis_file_path': 'analysis/react_analysis.html',
                'description': 'React.js是一个用于构建用户界面的JavaScript库',
                'is_published': True
            },
            {
                'project_name': 'Vue.js',
                'official_website': 'https://vuejs.org',
                'creator_name': '研究员B',
                'report_file_path': 'reports/vue_report.md',
                'analysis_file_path': 'analysis/vue_analysis.html',
                'description': 'Vue.js是一个渐进式JavaScript框架',
                'is_published': True
            }
        ]
        
        for report_data in sample_reports:
            # 检查是否已存在
            existing = db_service.execute_query(
                'research_reports',
                'select',
                filters={'project_name': report_data['project_name']}
            )
            
            if not existing.data:
                result = db_service.execute_query(
                    'research_reports',
                    'insert',
                    data=report_data
                )
                
                if result.data:
                    print(f"创建示例报告: {report_data['project_name']}")
        
        # 创建示例用户请求
        sample_requests = [
            {
                'user_email': '<EMAIL>',
                'project_name': 'Angular',
                'official_website': 'https://angular.io',
                'status': 'pending'
            },
            {
                'user_email': '<EMAIL>',
                'project_name': 'Svelte',
                'official_website': 'https://svelte.dev',
                'status': 'processing'
            }
        ]
        
        for request_data in sample_requests:
            # 检查是否已存在
            existing = db_service.execute_query(
                'user_requests',
                'select',
                filters={
                    'user_email': request_data['user_email'],
                    'project_name': request_data['project_name']
                }
            )
            
            if not existing.data:
                result = db_service.execute_query(
                    'user_requests',
                    'insert',
                    data=request_data
                )
                
                if result.data:
                    print(f"创建示例请求: {request_data['project_name']}")
        
        print("示例数据创建完成！")
    
    except Exception as e:
        print(f"创建示例数据时出错: {e}")

def create_sample_files():
    """创建示例文件"""
    try:
        # 确保目录存在
        os.makedirs('uploads/reports', exist_ok=True)
        os.makedirs('uploads/analysis', exist_ok=True)
        
        # 创建示例Markdown报告
        react_report = """# React.js 项目研究报告

## 项目概述

React.js是由Facebook开发的一个用于构建用户界面的JavaScript库。它采用组件化的开发模式，使得开发者可以创建可重用的UI组件。

## 主要特性

### 1. 虚拟DOM
React使用虚拟DOM来提高性能，通过diff算法最小化实际DOM操作。

### 2. 组件化
- 函数组件
- 类组件
- Hooks

### 3. 单向数据流
React采用单向数据流的设计模式，使得数据流向更加可预测。

## 技术架构

```javascript
function App() {
  const [count, setCount] = useState(0);
  
  return (
    <div>
      <h1>计数器: {count}</h1>
      <button onClick={() => setCount(count + 1)}>
        增加
      </button>
    </div>
  );
}
```

## 生态系统

| 工具 | 用途 | 官网 |
|------|------|------|
| Create React App | 脚手架 | https://create-react-app.dev |
| Next.js | 全栈框架 | https://nextjs.org |
| React Router | 路由 | https://reactrouter.com |

## 总结

React.js是一个成熟且强大的前端框架，适合构建各种规模的应用程序。
"""
        
        with open('uploads/reports/react_report.md', 'w', encoding='utf-8') as f:
            f.write(react_report)
        
        # 创建示例HTML分析页面
        react_analysis = """<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>React.js 分析页面</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .chart-container { width: 100%; height: 400px; margin: 20px 0; }
        .stats { display: flex; justify-content: space-around; margin: 20px 0; }
        .stat-item { text-align: center; padding: 20px; background: #f5f5f5; border-radius: 8px; }
    </style>
</head>
<body>
    <h1>React.js 项目分析</h1>
    
    <div class="stats">
        <div class="stat-item">
            <h3>GitHub Stars</h3>
            <p>200K+</p>
        </div>
        <div class="stat-item">
            <h3>NPM 下载量</h3>
            <p>20M+/周</p>
        </div>
        <div class="stat-item">
            <h3>社区活跃度</h3>
            <p>非常高</p>
        </div>
    </div>
    
    <div class="chart-container">
        <canvas id="popularityChart"></canvas>
    </div>
    
    <script>
        const ctx = document.getElementById('popularityChart').getContext('2d');
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['2018', '2019', '2020', '2021', '2022', '2023', '2024'],
                datasets: [{
                    label: 'GitHub Stars (K)',
                    data: [120, 140, 160, 175, 185, 195, 200],
                    borderColor: 'rgb(75, 192, 192)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    </script>
</body>
</html>"""
        
        with open('uploads/analysis/react_analysis.html', 'w', encoding='utf-8') as f:
            f.write(react_analysis)
        
        print("示例文件创建完成！")
    
    except Exception as e:
        print(f"创建示例文件时出错: {e}")

if __name__ == '__main__':
    print("正在初始化数据...")
    
    # 测试数据库连接
    if not db_service.test_connection():
        print("数据库连接失败，请检查配置")
        sys.exit(1)
    
    create_admin_user()
    create_sample_data()
    create_sample_files()
    
    print("\n初始化完成！")
    print("现在可以运行应用: python run.py")
